pub mod ai_message;
pub mod colors;
pub mod common;
pub mod rounded_message;
pub mod system_message;
pub mod user_message;

use crate::app::{AppMessage, MessageSender};
pub use common::truncate_tool_output;
use std::io;

use crate::display::colors::BLUE_COLOR;
use crossterm::style::Stylize;

pub fn print_formatted_message(app_msg: &AppMessage) -> io::Result<()> {
    match app_msg.sender {
        MessageSender::User => user_message::print_user_message(app_msg),
        MessageSender::AI => ai_message::print_ai_message(app_msg),
        MessageSender::System | MessageSender::ToolExecution => {
            system_message::print_system_message(app_msg)
        }
    }
}

pub fn print_command_output(message: &str) -> Result<(), Box<dyn std::error::Error>> {
    let (terminal_width, _) = crossterm::terminal::size().unwrap_or((80, 24));
    let width = terminal_width as usize;

    // Create blue rounded border
    let top_border = format!("╭{}╮", "─".repeat(width.saturating_sub(2)));
    let bottom_border = format!("╰{}╯", "─".repeat(width.saturating_sub(2)));

    println!("{}", top_border.with(BLUE_COLOR));

    // Add message content with padding
    for line in message.lines() {
        let content_width = width.saturating_sub(4); // Account for borders and padding
        if line.len() > content_width {
            // Wrap long lines
            for chunk in line.chars().collect::<Vec<_>>().chunks(content_width) {
                let chunk_str: String = chunk.iter().collect();
                println!(
                    "│ {} │",
                    format!("{:<width$}", chunk_str, width = content_width).with(BLUE_COLOR)
                );
            }
        } else {
            println!(
                "│ {} │",
                format!("{:<width$}", line, width = content_width).with(BLUE_COLOR)
            );
        }
    }

    println!("{}", bottom_border.with(BLUE_COLOR));
    Ok(())
}
