use crate::app::{AppMessage, MessageContent};
use crossterm::style::{Print, Stylize};
use std::io::{self, stdout, Write};

pub fn print_user_message(app_msg: &AppMessage) -> io::Result<()> {
    let mut out = stdout();
    let text_content = if let Some(MessageContent::Text(text)) = app_msg.parts.first() {
        text.lines().next().unwrap_or("").trim()
    } else {
        ""
    };
    let colored_text = format!("{}", text_content.cyan());
    crossterm::execute!(out, Print(format!("> {}\n", colored_text)))?;
    out.flush()
}
