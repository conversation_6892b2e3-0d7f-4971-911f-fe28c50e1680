use reedline::{Prompt, PromptEditMode, PromptHistorySearch, PromptHistorySearchStatus};
use std::borrow::Cow;

#[derive(Default, <PERSON>lone)]
pub struct CurrentPromptBufferState {
    pub current_buffer_content: String,
}

#[derive(Default)]
pub struct TokenCache {
    pub system_prompt_tokens: usize,
    pub history_tokens: usize,
    pub history_version: usize, // Track when history changes
}

#[derive(Clone)]
pub struct TokenCountingPrompt {}

impl Prompt for TokenCountingPrompt {
    fn render_prompt_left(&self) -> Cow<str> {
        Cow::Owned("".to_string())
    }

    fn render_prompt_right(&self) -> Cow<str> {
        Cow::Borrowed("")
    }

    fn render_prompt_indicator(&self, _edit_mode: PromptEditMode) -> Cow<str> {
        Cow::Borrowed("> ")
    }

    fn render_prompt_multiline_indicator(&self) -> Cow<str> {
        Cow::Borrowed("    ")
    }

    fn render_prompt_history_search_indicator(
        &self,
        history_search: PromptHistorySearch,
    ) -> Cow<str> {
        let prefix = match history_search.status {
            PromptHistorySearchStatus::Passing => "",
            PromptHistorySearchStatus::Failing => "failing ",
        };
        Cow::Owned(format!(
            "({}reverse-search: {}) ",
            prefix, history_search.term
        ))
    }

    fn right_prompt_on_last_line(&self) -> bool {
        true
    }
}
