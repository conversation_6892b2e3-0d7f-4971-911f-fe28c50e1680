use super::tools::create_tools;
use super::types::{ChatCompletionRequest, ChatCompletionResponse, ChatMessage, LLMError};
use crate::cli::{Cli, ModelType};
use crate::llm_client::provider::dummy_provider::call_dummy_provider;
use crate::utils::abort_signal::AbortSignal;
use reqwest::Client;
use std::time::Duration;
use tokio::time::sleep;

const POLLINATIONS_API_URL: &str = "https://text.pollinations.ai/openai/v1/chat/completions";

// Helper function to make a single API call attempt
async fn make_api_call_attempt(
    client: &Client,
    request_payload: &ChatCompletionRequest,
    abort_signal: &AbortSignal,
) -> Result<String, LLMError> {
    if abort_signal.aborted() {
        return Err(LLMError::ApiError("Request cancelled by user".to_string()));
    }
    let request = client.post(POLLINATIONS_API_URL).json(request_payload);
    let response = request.send().await?;
    if abort_signal.aborted() {
        return Err(LLMError::ApiError("Request cancelled by user".to_string()));
    }
    if response.status().is_success() {
        let chat_response = response.json::<ChatCompletionResponse>().await?;
        if let Some(choice) = chat_response.choices.first() {
            // Check if the message has tool calls
            if let Some(tool_calls) = &choice.message.tool_calls {
                if !tool_calls.is_empty() {
                    // Return the tool calls as JSON for processing
                    let tool_calls_json = serde_json::json!({
                        "tool_calls": tool_calls
                    });
                    return Ok(tool_calls_json.to_string());
                }
            }
            // Return regular content if no tool calls
            Ok(choice.message.content.clone())
        } else {
            Err(LLMError::Parse("No choices in response".to_string()))
        }
    } else {
        let status = response.status();
        let error_body = response
            .text()
            .await
            .unwrap_or_else(|_| "Could not retrieve error body".to_string());
        if error_body.trim().is_empty() {
            return Err(LLMError::ApiError(format!(
                "Status {}: No error body provided",
                status
            )));
        }
        Err(LLMError::ApiError(format!(
            "Status {}: {}",
            status, error_body
        )))
    }
}

pub async fn call_llm_api(
    client: &Client,
    messages_for_api_call: Vec<ChatMessage>,
    abort_signal: &AbortSignal,
    cli_args: &Cli,
) -> Result<String, LLMError> {
    match cli_args.model {
        ModelType::InstantDummy => {
            return call_dummy_provider(None, abort_signal).await;
        }
        ModelType::DelayedDummy => {
            return call_dummy_provider(Some(1000), abort_signal).await;
        }
        ModelType::DelayedLongDummy => {
            return call_dummy_provider(Some(10000), abort_signal).await;
        }
    
        _ => {} // Continue with API call for other models
    }

    let model_name = cli_args.model.api_model_name();
    let tools = if cli_args.no_tools {
        None
    } else {
        Some(create_tools())
    };
    let request_payload = ChatCompletionRequest {
        model: model_name.to_string(),
        messages: messages_for_api_call,
        stream: false,
        tools,
    };

    let retry_delays = [
        Duration::from_millis(200),
        Duration::from_millis(400),
        Duration::from_millis(800),
        Duration::from_millis(1600),
        Duration::from_millis(3200),
        Duration::from_millis(6400),
        Duration::from_millis(12800),
        Duration::from_millis(25600),
        Duration::from_millis(51200),
    ];
    let max_retries = retry_delays.len();

    // First attempt
    let mut result = make_api_call_attempt(client, &request_payload, abort_signal).await;

    // Retry loop
    let mut attempt = 0;
    while result.is_err() && attempt < max_retries {
        // Check for cancellation before retrying
        if abort_signal.aborted() {
            return Err(LLMError::ApiError("Request cancelled by user".to_string()));
        }

        let delay = retry_delays[attempt];
        sleep(delay).await;

        // Check again after sleep
        if abort_signal.aborted() {
            return Err(LLMError::ApiError("Request cancelled by user".to_string()));
        }

        attempt += 1;
        result = make_api_call_attempt(client, &request_payload, abort_signal).await;
    }

    // If we've exhausted all retries and still have an error, add context to the error
    if result.is_err() && attempt > 0 {
        let err = result.unwrap_err();
        return Err(LLMError::ApiError(format!(
            "{} (after {} retries)",
            err, attempt
        )));
    }

    result
}
