use crate::llm_client::types::LLMError;
use crate::utils::AbortSignal;
use std::time::Duration;
use tokio::time::sleep;

pub async fn call_dummy_provider(
    delay_ms: Option<u64>,
    abort_signal: &AbortSignal,
) -> Result<String, LLMError> {
    match delay_ms {
        None => Ok(include_str!("../../../tests/markdown_features_demo.md").into()),
        Some(delay) => {
            let chunks = 10;
            let chunk_delay = Duration::from_millis(delay) / chunks;
            for _ in 0..chunks {
                if abort_signal.aborted() {
                    return Err(LLMError::ApiError("Request cancelled by user".to_string()));
                }
                sleep(chunk_delay).await;
            }
            if abort_signal.aborted() {
                Err(LLMError::ApiError("Request cancelled by user".to_string()))
            } else {
                Ok(include_str!("../../../tests/markdown_features_demo.md").into())
            }
        }
    }
}
