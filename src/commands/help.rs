use crate::display::colors::INFO_BORDER_COLOR;
use crate::display::rounded_message::print_rounded_message;

pub fn print_help() {
    let help_text = r#"Available commands:

• Ctrl-D to quit
• Ctrl-C to abort network request, clear prompt or exit if empty
• Cmd+Enter for newline

• /session - Copy session ID to clipboard
• /last - Copy last AI response to clipboard
• /all - Copy all conversation history to clipboard
• /model [name] - Show current model or change to specified model"#;
    if let Err(e) = print_rounded_message(help_text, INFO_BORDER_COLOR) {
        eprintln!("Error displaying help: {}", e);
    }
}
