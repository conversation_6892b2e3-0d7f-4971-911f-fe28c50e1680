use crate::llm_client::{ChatMessage, ToolCallAction};
use pulldown_cmark::{CodeBlockKind, Event, Options, Parser, Tag, TagEnd};
use clap::Parser as ClapParser;
use crate::llm_client::system_prompt::create_system_prompt;
use crate::llm_client::tools_parsing::try_parse_tool_call;
use crate::cli::ModelType;

#[derive(Clone, Debug, PartialEq)]
pub enum MessageContent {
    Text(String),
    FormattedText(Vec<FormattedTextElement>),
    CodeBlock {
        language: Option<String>,
        content: String,
    },
    ToolCall(ToolCallAction),
}

#[derive(Clone, Debug, PartialEq)]
pub enum FormattedTextElement {
    Text(String),
    Heading { level: u8, text: String },
    BoldOrItalic(String),
    Strikethrough(String),
    InlineCode(String),
    ListItem { indent_level: u8, text: String, is_ordered: bool, number: Option<u64> },
    ListStart, // Marker for the start of a list
    ListEnd,   // Marker for the end of a list
    Link { text: String, url: String, title: Option<String> },
    Table { headers: Vec<String>, rows: Vec<Vec<String>>, alignments: Vec<TableAlignment> },
    LineBreak,
    HorizontalRuler,
}

#[derive(Clone, Debug, PartialEq)]
pub enum TableAlignment {
    None,
    Left,
    Center,
    Right,
}

#[derive(Clone, Debug, PartialEq)]
pub enum MessageSender {
    User,
    AI,
    System,
    ToolExecution,
}

impl MessageSender {
    pub fn as_str(&self) -> &'static str {
        match self {
            MessageSender::User => "User",
            MessageSender::AI => "AI",
            MessageSender::System => "System",
            MessageSender::ToolExecution => "Tool Execution",
        }
    }
}

#[derive(Clone, Debug)]
pub struct AppMessage {
    pub sender: MessageSender,
    pub parts: Vec<MessageContent>,
}

pub struct App {
    #[allow(dead_code)]
    pub http_client: reqwest::Client, // Used in handler.rs but passed separately
    pub messages: Vec<AppMessage>, // For display structuring and logging
    pub conversation_history_for_llm: Vec<ChatMessage>, // For actual LLM context
    pub should_quit: bool,
    pub ai_is_thinking: bool, // Track when AI is processing to show interrupt message
    pub current_model: ModelType, // Current model being used
}

impl App {
    pub fn new(http_client: reqwest::Client, initial_model: ModelType) -> Self {
        App {
            http_client,
            messages: Vec::new(),
            conversation_history_for_llm: Vec::new(),
            should_quit: false,
            ai_is_thinking: false,
            current_model: initial_model,
        }
    }

    /// Change the current model at runtime
    pub fn change_model(&mut self, new_model: ModelType) -> Result<String, String> {
        if !new_model.supports_tools() {
            return Err(format!("Model {} does not support tools", new_model.to_string_value()));
        }

        let old_model = self.current_model;
        self.current_model = new_model;

        Ok(format!(
            "Model changed from {} to {}",
            old_model.startup_banner_text(),
            new_model.startup_banner_text()
        ))
    }

    /// Get available models for runtime switching
    pub fn get_available_models(&self) -> Vec<ModelType> {
        ModelType::tool_supported_models()
    }

    pub fn add_user_message_to_llm_history(&mut self, content: String) {
        self.conversation_history_for_llm.push(ChatMessage {
            role: "user".to_string(),
            content,
            tool_calls: None,
            tool_call_id: None,
        });
    }

    pub fn add_assistant_response_to_llm_history(&mut self, raw_content: String) {
        // Check if this is a tool call response
        if let Some(tool_call) = try_parse_tool_call(&raw_content) {
            // For tool calls, we need to store them in the proper OpenAI format
            let tool_calls = vec![crate::llm_client::types::ToolCall {
                id: tool_call.id.unwrap_or_else(|| format!("call_{}", &uuid::Uuid::new_v4().to_string().replace('-', "")[..8])),
                call_type: "function".to_string(),
                function: crate::llm_client::types::ToolCallFunction {
                    name: tool_call.tool_name,
                    arguments: serde_json::to_string(&tool_call.arguments).unwrap_or_else(|_| "{}".to_string()),
                },
            }];

            self.conversation_history_for_llm.push(ChatMessage {
                role: "assistant".to_string(),
                content: "".to_string(), // Empty content when using tool calls
                tool_calls: Some(tool_calls),
                tool_call_id: None,
            });
        } else {
            // Regular text/markdown response
            self.conversation_history_for_llm.push(ChatMessage {
                role: "assistant".to_string(),
                content: raw_content,
                tool_calls: None,
                tool_call_id: None,
            });
        }
    }

    pub fn add_tool_response_to_llm_history(&mut self, tool_call_id: &str, response_content: String) {
        // For tool responses, we need to include the tool_call_id to match the call
        self.conversation_history_for_llm.push(ChatMessage {
            role: "tool".to_string(),
            content: response_content,
            tool_calls: None,
            tool_call_id: Some(tool_call_id.to_string()),
        });
    }

    /// Get the last AI message content for clipboard copying
    pub fn get_last_ai_message_content(&self) -> Option<String> {
        // Find the last AI message in the display messages
        for message in self.messages.iter().rev() {
            if message.sender == MessageSender::AI {
                return Some(self.format_message_content_as_text(message));
            }
        }
        None
    }

    /// Get all messages formatted for API communication (for /all command)
    pub fn get_all_messages_for_api(&self) -> String {
        let mut result = Vec::new();

        // Add system prompt info
        result.push("=== SYSTEM PROMPT ===".to_string());

        // Get current working directory for workspace injection
        let workspace_path = std::env::current_dir().ok();
        let system_prompt_content = create_system_prompt(
            &<crate::cli::Cli as ClapParser>::parse(),
            workspace_path.as_deref()
        );
        result.push(system_prompt_content);
        result.push("".to_string());

        // Add all conversation history
        result.push("=== CONVERSATION HISTORY ===".to_string());
        for (i, msg) in self.conversation_history_for_llm.iter().enumerate() {
            result.push(format!("Message {}: Role: {}", i + 1, msg.role));
            if !msg.content.is_empty() {
                result.push(format!("Content: {}", msg.content));
            }
            if let Some(tool_calls) = &msg.tool_calls {
                result.push("Tool Calls:".to_string());
                for tool_call in tool_calls {
                    result.push(format!("  - {}: {}", tool_call.function.name, tool_call.function.arguments));
                }
            }
            if let Some(tool_call_id) = &msg.tool_call_id {
                result.push(format!("Tool Call ID: {}", tool_call_id));
            }
            result.push("".to_string());
        }

        result.join("\n")
    }

    /// Format a message's content as plain text
    fn format_message_content_as_text(&self, message: &AppMessage) -> String {
        let mut result = Vec::new();

        for part in &message.parts {
            match part {
                MessageContent::Text(text) => {
                    result.push(text.clone());
                }
                MessageContent::FormattedText(elements) => {
                    for element in elements {
                        match element {
                            FormattedTextElement::Text(text) => result.push(text.clone()),
                            FormattedTextElement::Heading { text, level } => {
                                result.push(format!("{} {}", "#".repeat(*level as usize), text));
                            }
                            FormattedTextElement::BoldOrItalic(text) => result.push(format!("**{}**", text)),
                            FormattedTextElement::Strikethrough(text) => result.push(format!("~~{}~~", text)),
                            FormattedTextElement::InlineCode(text) => result.push(format!("`{}`", text)),
                            FormattedTextElement::ListItem { text, is_ordered, number, .. } => {
                                if *is_ordered {
                                    if let Some(num) = number {
                                        result.push(format!("{}. {}", num, text));
                                    } else {
                                        result.push(format!("- {}", text));
                                    }
                                } else {
                                    result.push(format!("- {}", text));
                                }
                            }
                            FormattedTextElement::Link { text, url, .. } => {
                                result.push(format!("[{}]({})", text, url));
                            }
                            FormattedTextElement::HorizontalRuler => result.push("---".to_string()),
                            FormattedTextElement::LineBreak => result.push("\n".to_string()),
                            _ => {} // Skip other elements for now
                        }
                    }
                }
                MessageContent::CodeBlock { language, content } => {
                    if let Some(lang) = language {
                        result.push(format!("```{}\n{}\n```", lang, content));
                    } else {
                        result.push(format!("```\n{}\n```", content));
                    }
                }
                MessageContent::ToolCall(tool_call) => {
                    result.push(format!("Tool Call: {} with args: {:?}", tool_call.tool_name, tool_call.arguments));
                }
            }
        }

        result.join("\n")
    }

    pub fn create_ai_app_message_from_raw(&self, raw_content: &str) -> AppMessage {
        // This function parses the AI's raw response string into structured AppMessage for display.
        // It checks if the response is a tool call or regular text/markdown.
        log::debug!("Processing raw AI content of length: {}", raw_content.len());
        
        if let Some(tool_call) = try_parse_tool_call(raw_content) {
            log::debug!("Detected tool call in AI response: {}", tool_call.tool_name);
            let app_message = AppMessage {
                sender: MessageSender::AI,
                parts: vec![MessageContent::ToolCall(tool_call.clone())],
            };
            crate::logging::log_ai_message_structure("tool_call", raw_content, &app_message.parts);
            app_message
        } else {
            log::debug!("No tool call detected, parsing as markdown");
            // Always parse markdown to get proper formatting
            // Only fall back to plain text if parsing produces no meaningful structure

            // Parse markdown with proper formatting support
            let mut parts = Vec::new();
            let mut formatted_elements = Vec::new();
            let parser = Parser::new_ext(
                raw_content,
                Options::ENABLE_STRIKETHROUGH | Options::ENABLE_TABLES | Options::ENABLE_TASKLISTS
            );
            let mut in_code_block_lang: Option<String> = None;
            let mut in_heading_level: Option<u8> = None;
            let mut in_strong_or_italic = false;
            let mut in_strikethrough = false;

            let mut in_list_item = false;
            let mut list_depth: u8 = 0;
            let mut current_list_is_ordered = false;
            let mut current_list_number: Option<u64> = None;
            let mut current_list_item_text = String::new();
            // We don't need to track individual list items anymore since we're using ListStart/ListEnd markers

            // Link state
            let mut in_link = false;
            let mut link_url = String::new();
            let mut link_title: Option<String> = None;
            let mut link_text = String::new();

            // Table state
            let mut in_table = false;
            let mut table_headers: Vec<String> = Vec::new();
            let mut table_rows: Vec<Vec<String>> = Vec::new();
            let mut table_alignments: Vec<TableAlignment> = Vec::new();
            let mut current_table_row: Vec<String> = Vec::new();
            let mut current_table_cell = String::new();
            let mut in_table_head = false;
            let mut paragraph_count = 0;

            for event in parser {
                match event {
                    Event::Start(Tag::Paragraph) => {
                        // Add a line break before the paragraph if this is not the first paragraph
                        if paragraph_count > 0 {
                            formatted_elements.push(FormattedTextElement::LineBreak);
                        }
                        paragraph_count += 1;
                    }
                    Event::End(TagEnd::Paragraph) => {
                        // Paragraph end is handled implicitly
                    }
                    Event::Start(Tag::CodeBlock(CodeBlockKind::Fenced(lang))) => {
                        // Flush any accumulated formatted elements
                        if !formatted_elements.is_empty() {
                            parts.push(MessageContent::FormattedText(formatted_elements.clone()));
                            formatted_elements.clear();
                        }

                        let lang_str = lang.into_string();
                        in_code_block_lang = Some(lang_str.clone());
                        parts.push(MessageContent::CodeBlock {
                            language: Some(lang_str),
                            content: String::new(),
                        });
                    }
                    Event::End(TagEnd::CodeBlock) => {
                        in_code_block_lang = None;
                    }
                    Event::Start(Tag::Heading { level, .. }) => {
                        in_heading_level = Some(level as u8);
                    }
                    Event::End(TagEnd::Heading { .. }) => {
                        in_heading_level = None;
                    }
                    Event::Start(Tag::Strikethrough) => {
                        in_strikethrough = true;
                    }
                    Event::End(TagEnd::Strikethrough) => {
                        in_strikethrough = false;
                    }
                    Event::Start(Tag::Emphasis) => {
                        in_strong_or_italic = true;
                    }
                    Event::End(TagEnd::Emphasis) => {
                        in_strong_or_italic = false;
                    }
                    Event::Start(Tag::Strong) => {
                        in_strong_or_italic = true;
                    }
                    Event::End(TagEnd::Strong) => {
                        in_strong_or_italic = false;
                    }
                    Event::Code(code_text) => {
                        // Inline code is a single event in pulldown-cmark
                        if in_list_item {
                            // For list items, accumulate the inline code into the list item text
                            current_list_item_text.push_str(&format!("`{}`", code_text));
                        } else {
                            formatted_elements
                                .push(FormattedTextElement::InlineCode(code_text.to_string()));
                        }
                    }
                    Event::Start(Tag::Item) => {
                        in_list_item = true;
                        current_list_item_text.clear();
                    }
                    Event::End(TagEnd::Item) => {
                        // Add the accumulated list item text
                        if !current_list_item_text.is_empty() {
                            formatted_elements.push(FormattedTextElement::ListItem {
                                indent_level: list_depth.saturating_sub(1),
                                text: current_list_item_text.clone(),
                                is_ordered: current_list_is_ordered,
                                number: current_list_number,
                            });
                            
                            // Increment list number for next item if ordered
                            if current_list_is_ordered {
                                current_list_number = current_list_number.map(|n| n + 1);
                            }
                            
                            current_list_item_text.clear();
                        }
                        in_list_item = false;
                    }
                    Event::Start(Tag::List(first_item_number)) => {
                        list_depth += 1;
                        current_list_is_ordered = first_item_number.is_some();
                        current_list_number = first_item_number;
                        
                        // Add a marker for list start if this is a top-level list
                        if list_depth == 1 {
                            formatted_elements.push(FormattedTextElement::ListStart);
                        }
                    }
                    Event::End(TagEnd::List(_)) => {
                        if list_depth == 1 {
                            // Add a marker for list end if this is a top-level list
                            formatted_elements.push(FormattedTextElement::ListEnd);
                        }
                        
                        list_depth = list_depth.saturating_sub(1);
                        if list_depth == 0 {
                            current_list_is_ordered = false;
                            current_list_number = None;
                        }
                    }
                    Event::Start(Tag::Link { dest_url, title, .. }) => {
                        in_link = true;
                        link_url = dest_url.to_string();
                        link_title = if title.is_empty() { None } else { Some(title.to_string()) };
                        link_text.clear();
                    }
                    Event::End(TagEnd::Link) => {
                        if in_link {
                            if in_list_item {
                                // For list items with links, add special marker with both text and URL
                                current_list_item_text.push_str(&format!("__LINK_START__{}__LINK_URL__{}__LINK_END__", 
                                    link_text, link_url));
                            } else {
                                formatted_elements.push(FormattedTextElement::Link {
                                    text: link_text.clone(),
                                    url: link_url.clone(),
                                    title: link_title.clone(),
                                });
                            }
                            in_link = false;
                            link_text.clear();
                            link_url.clear();
                            link_title = None;
                        }
                    }
                    Event::Start(Tag::Table(alignments)) => {
                        // Flush any accumulated formatted elements before starting table
                        if !formatted_elements.is_empty() {
                            parts.push(MessageContent::FormattedText(formatted_elements.clone()));
                            formatted_elements.clear();
                        }

                        in_table = true;
                        table_alignments = alignments.iter().map(|align| {
                            match align {
                                pulldown_cmark::Alignment::None => TableAlignment::None,
                                pulldown_cmark::Alignment::Left => TableAlignment::Left,
                                pulldown_cmark::Alignment::Center => TableAlignment::Center,
                                pulldown_cmark::Alignment::Right => TableAlignment::Right,
                            }
                        }).collect();
                        table_headers.clear();
                        table_rows.clear();
                    }
                    Event::End(TagEnd::Table) => {
                        if in_table {
                            formatted_elements.push(FormattedTextElement::Table {
                                headers: table_headers.clone(),
                                rows: table_rows.clone(),
                                alignments: table_alignments.clone(),
                            });
                            in_table = false;
                            in_table_head = false;
                        }
                    }
                    Event::Start(Tag::TableHead) => {
                        in_table_head = true;
                        current_table_row.clear();
                    }
                    Event::End(TagEnd::TableHead) => {
                        if in_table_head {
                            table_headers = current_table_row.clone();
                            current_table_row.clear();
                            in_table_head = false;
                        }
                    }
                    Event::Start(Tag::TableRow) => {
                        current_table_row.clear();
                    }
                    Event::End(TagEnd::TableRow) => {
                        if !in_table_head && in_table {
                            table_rows.push(current_table_row.clone());
                        }
                        current_table_row.clear();
                    }
                    Event::Start(Tag::TableCell) => {
                        current_table_cell.clear();
                    }
                    Event::End(TagEnd::TableCell) => {
                        current_table_row.push(current_table_cell.clone());
                        current_table_cell.clear();
                    }
                    Event::Text(text_cow) => {
                        if in_code_block_lang.is_some() {
                            // Handle code block content
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push_str(&text_cow);
                            }
                        } else if in_table {
                            // Handle table cell content
                            current_table_cell.push_str(&text_cow);
                        } else if in_link {
                            // Handle link text
                            link_text.push_str(&text_cow);
                        } else if let Some(level) = in_heading_level {
                            // Handle heading text
                            formatted_elements.push(FormattedTextElement::Heading {
                                level,
                                text: text_cow.to_string(),
                            });
                        } else if in_strong_or_italic {
                            if in_list_item {
                                // For list items with bold/italic, accumulate the formatted text
                                current_list_item_text.push_str(&format!("**{}**", text_cow));
                            } else {
                                formatted_elements
                                    .push(FormattedTextElement::BoldOrItalic(text_cow.to_string()));
                            }
                        } else if in_strikethrough {
                            if in_list_item {
                                // For list items with strikethrough, accumulate the formatted text
                                current_list_item_text.push_str(&format!("~~{}~~", text_cow));
                            } else {
                                formatted_elements
                                    .push(FormattedTextElement::Strikethrough(text_cow.to_string()));
                            }
                        } else if in_list_item {
                            // Accumulate text for the current list item
                            current_list_item_text.push_str(&text_cow);
                        } else {
                            // Handle regular text
                            log::debug!("Adding regular text element: '{}'", text_cow);
                            formatted_elements
                                .push(FormattedTextElement::Text(text_cow.to_string()));
                        }
                    }
                    Event::Rule => {
                        formatted_elements.push(FormattedTextElement::HorizontalRuler);
                    }
                    Event::HardBreak => {
                        if in_code_block_lang.is_some() {
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push('\n');
                            }
                        } else if in_list_item {
                            // For list items, add a space for hard breaks
                            current_list_item_text.push(' ');
                        } else {
                            formatted_elements.push(FormattedTextElement::LineBreak);
                        }
                    }
                    Event::SoftBreak => {
                        if in_code_block_lang.is_some() {
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push('\n');
                            }
                        } else if in_list_item {
                            // For list items, add a space for soft breaks
                            current_list_item_text.push(' ');
                        } else {
                            formatted_elements.push(FormattedTextElement::Text(" ".to_string()));
                        }
                    }
                    Event::Html(html_cow) => {
                        // Treat HTML as text
                        if in_code_block_lang.is_some() {
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push_str(&html_cow);
                            }
                        } else {
                            formatted_elements
                                .push(FormattedTextElement::Text(html_cow.to_string()));
                        }
                    }
                    _ => {}
                }
            }

            // Add any remaining formatted elements
            let has_formatted_elements = !formatted_elements.is_empty();
            if has_formatted_elements {
                parts.push(MessageContent::FormattedText(formatted_elements));
            }

            // Fallback if parsing results in no parts but raw_content is not empty
            if parts.is_empty() && !raw_content.is_empty() {
                // Fall back to plain text if no meaningful structure was found
                log::debug!("No parts found after parsing, falling back to plain text");
                parts.push(MessageContent::Text(raw_content.to_string()));
            }

            let app_message = AppMessage {
                sender: MessageSender::AI,
                parts,
            };
            
            // Log the final structure of the message
            crate::logging::log_ai_message_structure("markdown_parsed", raw_content, &app_message.parts);
            
            app_message
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_markdown_processing() {
        let test_markdown = r#"### Test Heading

This is regular text with **bold content** and some `inline code` here.

## Another Heading
- First list item with **bold**
- Second list item with `code`"#;

        let app = App::new(reqwest::Client::new());
        let app_message = app.create_ai_app_message_from_raw(test_markdown);

        // Should have FormattedText content with proper markdown parsing
        assert_eq!(app_message.parts.len(), 1);
        match &app_message.parts[0] {
            MessageContent::FormattedText(elements) => {
                // Should have headings, text, bold, and inline code elements
                assert!(elements.len() > 5);

                // Verify we have the expected element types
                let has_heading = elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::Heading { .. }));
                let has_bold = elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::BoldOrItalic(_)));
                let has_inline_code = elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::InlineCode(_)));
                let has_list_item = elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::ListItem { .. }));
                    
                let has_list_markers = elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::ListStart)) &&
                    elements
                    .iter()
                    .any(|e| matches!(e, FormattedTextElement::ListEnd));

                assert!(has_heading, "Should have heading elements");
                assert!(has_bold, "Should have bold elements");
                assert!(has_inline_code, "Should have inline code elements");
                assert!(has_list_item, "Should have list item elements");
                assert!(has_list_markers, "Should have list start and end markers");
            }
            _ => panic!("Expected FormattedText content"),
        }
    }

    #[test]
    fn test_enhanced_markdown_features() {
        let test_markdown = r#"# Main Heading

## Links and Tables

Check out [Rust](https://rust-lang.org "The Rust Programming Language").

| Name | Value |
|------|-------|
| Test | 123   |
| Demo | 456   |

### Ordered Lists
1. First item
2. Second item
3. Third item"#;

        let app = App::new(reqwest::Client::new());
        let app_message = app.create_ai_app_message_from_raw(test_markdown);

        // Should have multiple parts due to table
        assert!(!app_message.parts.is_empty());

        // Check for various element types across all parts
        let mut has_h1 = false;
        let mut has_link = false;
        let mut has_table = false;
        let mut has_ordered_list = false;
        let mut has_list_start = false;
        let mut has_list_end = false;

        for part in &app_message.parts {
            if let MessageContent::FormattedText(elements) = part {
                for element in elements {
                    match element {
                        FormattedTextElement::Heading { level: 1, .. } => has_h1 = true,
                        FormattedTextElement::Link { .. } => has_link = true,
                        FormattedTextElement::Table { .. } => has_table = true,
                        FormattedTextElement::ListItem { is_ordered: true, .. } => has_ordered_list = true,
                        FormattedTextElement::ListStart => has_list_start = true,
                        FormattedTextElement::ListEnd => has_list_end = true,
                        _ => {}
                    }
                }
            }
        }

        assert!(has_h1, "Should have H1 heading");
        assert!(has_link, "Should have link elements");
        assert!(has_table, "Should have table elements");
        assert!(has_ordered_list, "Should have ordered list items");
        assert!(has_list_start, "Should have list start marker");
        assert!(has_list_end, "Should have list end marker");
    }
}
