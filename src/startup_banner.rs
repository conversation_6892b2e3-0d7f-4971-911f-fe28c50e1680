use crate::cli::ModelType;
use crossterm::style::Stylize;
use std::env;
use std::path::PathBuf;

/// Finds the git root directory by walking up the directory tree
fn find_git_root() -> Option<PathBuf> {
    let current_dir = env::current_dir().ok()?;
    let mut path = current_dir.as_path();

    loop {
        let git_dir = path.join(".git");
        if git_dir.exists() {
            return Some(path.to_path_buf());
        }

        match path.parent() {
            Some(parent) => path = parent,
            None => return None,
        }
    }
}

/// Prints a nicely formatted startup banner with agent info and session details
pub fn print_startup_banner(session_id: &str, proxy: Option<u16>, model: ModelType) {
    let session_display = if session_id.starts_with("session_") {
        "session:".to_owned() + &*session_id.trim_start_matches("session_").to_string()
    } else {
        "unknown".into()
    };

    let mut info_parts = Vec::new();
    if let Some(port) = proxy {
        info_parts.push(format!("proxy:{}", port));
    }
    info_parts.push(session_display);

    // Add git root or current directory information
    let working_dir = if let Some(git_root) = find_git_root() {
        format!("{}", git_root.display())
    } else if let Ok(current_dir) = env::current_dir() {
        format!("{}", current_dir.display())
    } else {
        "unknown directory".to_string()
    };

    println!(
        "\nDima AI Agent     {}    {}\n\nWorking in {}\n",
        model.startup_banner_text(),
        format!("[{}]", info_parts.join(", ")).yellow(),
        working_dir.blue()
    );
}
