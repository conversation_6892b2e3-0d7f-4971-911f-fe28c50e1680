mod app;
mod cli;
mod cli_prompt;
mod clipboard;
mod commands;
mod confetti;
mod display;
mod hammerspoon;
mod handler;
mod keyboard_modifier;
mod llm_client;
mod logging;
mod network;
mod session_manager;
mod startup_banner;
mod suspendable_editor;
mod syntax_highlighting;
mod token_counting_hinter;
pub mod utils;
mod workspace;

use crate::clipboard::copy_to_clipboard;
use crate::commands::help::print_help;
use crate::display::colors::ORANGE_COLOR;
use crate::display::rounded_message::print_rounded_message;
use crate::handler::user_input::process_user_input;
use crate::keyboard_modifier::{
    disable_kitty_keyboard_for_meta_alt_modifiers, enable_kitty_keyboard_for_meta_alt_modifiers,
};
use crate::session_manager::normalize_session_id;
use crate::startup_banner::print_startup_banner;
use crate::suspendable_editor::{ReadResult, SuspendableReedline};
use crate::token_counting_hinter::TokenCountingHinter;
use crate::utils::{create_abort_signal, poll_abort_signal, AbortSignal};
use app::{App, AppMessage, MessageContent, MessageSender};
use crossterm::style::Stylize;
use nu_ansi_term::{Color, Style};
use std::env::temp_dir;
use std::io::ErrorKind;
use std::process::Command as OsCommand;
use std::sync::{Arc, Mutex};
use std::time::Duration;

// Structure to track the current API task
struct ApiTaskManager {
    current_task: Option<tokio::task::JoinHandle<()>>,
    abort_signal: Option<AbortSignal>,
}

impl ApiTaskManager {
    fn new() -> Self {
        Self {
            current_task: None,
            abort_signal: None,
        }
    }

    fn start_task(&mut self, task: tokio::task::JoinHandle<()>, signal: AbortSignal) {
        // Cancel any existing task
        self.cancel_current_task();

        self.current_task = Some(task);
        self.abort_signal = Some(signal);
    }

    fn cancel_current_task(&mut self) {
        if let Some(signal) = &self.abort_signal {
            signal.set_ctrlc();
        }
        if let Some(task) = &self.current_task {
            task.abort();
        }
        self.current_task = None;
        self.abort_signal = None;
    }

    fn is_task_running(&self) -> bool {
        if let Some(task) = &self.current_task {
            !task.is_finished()
        } else {
            false
        }
    }

    fn check_and_cleanup_finished_task(&mut self) {
        if let Some(task) = &self.current_task {
            if task.is_finished() {
                self.current_task = None;
                self.abort_signal = None;
            }
        }
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logger for debugging
    logging::init_logger()?;
    log::info!("Starting rust_llm_tui with debugging enabled");

    let cli_args = cli::parse_cli_args();

    let request_timeout = Duration::from_secs(180);
    let http_client = network::create_client(request_timeout, &cli_args);
    let app_arc = Arc::new(Mutex::new(App::new(http_client.clone(), cli_args.model)));

    let session_dir_path = session_manager::ensure_session_dir_exists()?;

    let session_id = normalize_session_id(
        cli_args
            .restore
            .clone()
            .unwrap_or_else(session_manager::generate_new_session_id),
    );

    let session_file_path = session_manager::get_session_file_path(&session_dir_path, &session_id);
    print_startup_banner(&session_id, cli_args.proxy_localhost, cli_args.model);

    if cli_args.restore.is_some() || session_file_path.exists() {
        match session_manager::load_session(&session_file_path) {
            Ok(loaded_llm_history) => {
                if !loaded_llm_history.is_empty() {
                    println!("Restored session: {}", session_id.clone().green());
                    let mut constructed_app_messages = Vec::new();
                    {
                        let mut app_locked = app_arc.lock().unwrap();
                        app_locked.conversation_history_for_llm = loaded_llm_history;
                        for chat_msg in &app_locked.conversation_history_for_llm {
                            let display_msg = if chat_msg.role == "user" {
                                AppMessage {
                                    sender: MessageSender::User,
                                    parts: vec![MessageContent::Text(chat_msg.content.clone())],
                                }
                            } else if chat_msg.role == "assistant" {
                                app_locked.create_ai_app_message_from_raw(&chat_msg.content)
                            } else if chat_msg.role == "tool" {
                                AppMessage {
                                    sender: MessageSender::ToolExecution,
                                    parts: vec![MessageContent::Text(format!(
                                        "[Tool Result]\n{}",
                                        chat_msg.content.clone()
                                    ))],
                                }
                            } else {
                                continue;
                            };
                            constructed_app_messages.push(display_msg);
                        }
                    }
                    if !constructed_app_messages.is_empty() {
                        let mut app_locked = app_arc.lock().unwrap();
                        for msg in &constructed_app_messages {
                            app_locked.messages.push(msg.clone());
                        }
                    }
                    for msg_to_print in &constructed_app_messages {
                        display::print_formatted_message(msg_to_print)?;
                    }
                } else if cli_args.restore.is_some() {
                    println!(
                        "Starting new session (or session file was empty): {}",
                        session_id.clone().yellow()
                    );
                }
            }
            Err(e) if e.kind() == ErrorKind::NotFound => {
                println!(
                    "Starting new session (no existing file found for ID): {}",
                    session_id.clone().yellow()
                );
            }
            Err(e) => {
                eprintln!(
                    "Error loading session '{}': {}. Starting fresh.",
                    session_id.clone().red(),
                    e
                );
            }
        }
    } else {
        // Session info already shown in the banner
    }

    // Handle initial message if provided
    if let Some(initial_message) = &cli_args.initial_message {
        // Create a user AppMessage and display it
        let user_app_message = AppMessage {
            sender: MessageSender::User,
            parts: vec![MessageContent::Text(initial_message.clone())],
        };

        // Display user message
        if let Err(e) = display::print_formatted_message(&user_app_message) {
            eprintln!("Error displaying initial user message: {}", e);
        }

        // Add to app messages and LLM history
        {
            let mut app_locked = app_arc.lock().unwrap();
            app_locked.messages.push(user_app_message.clone());
            app_locked.add_user_message_to_llm_history(initial_message.clone());
            if let Err(e) = session_manager::save_session(
                &session_file_path,
                &app_locked.conversation_history_for_llm,
            ) {
                eprintln!("{}", format!("Error saving session: {}", e).red());
            }
        }

        // Create abort signal for initial message processing
        let abort_signal = create_abort_signal();

        // Set AI thinking state
        {
            let mut app_locked = app_arc.lock().unwrap();
            app_locked.ai_is_thinking = true;
        }

        // Process the initial message
        let app_arc_clone = app_arc.clone();
        let http_client_clone = http_client.clone();
        let session_file_path_clone = session_file_path.clone();
        let cli_args_clone = cli_args.clone();
        let abort_signal_clone = abort_signal.clone();
        let initial_message_clone = initial_message.clone();

        let task = tokio::spawn(async move {
            let result = process_user_input(
                initial_message_clone,
                app_arc_clone.clone(),
                &http_client_clone,
                &session_file_path_clone,
                &cli_args_clone,
                &abort_signal_clone,
                false, // Don't display user message - already shown
            )
            .await;

            // Clear AI thinking state when done
            {
                let mut app_locked = app_arc_clone.lock().unwrap();
                app_locked.ai_is_thinking = false;
            }

            if let Err(e) = result {
                eprintln!("Error processing initial message: {}", e);
            }
        });

        // Wait for the initial message processing to complete
        if let Err(e) = task.await {
            eprintln!("Error waiting for initial message task: {}", e);
        }
    }

    // Create shared state for token counting
    let prompt_buffer_state_arc =
        Arc::new(Mutex::new(cli_prompt::CurrentPromptBufferState::default()));
    let token_counting_prompt = cli_prompt::TokenCountingPrompt {};

    // Create API task manager
    let mut api_task_manager = ApiTaskManager::new();

    // Create the token counting hinter that will update buffer state in real-time
    let token_counting_hinter =
        TokenCountingHinter::new(app_arc.clone(), prompt_buffer_state_arc.clone(), &cli_args)
            .with_style(Style::new().fg(Color::DarkGray));

    // Create a suspendable editor with external editor support and custom hinter
    let temp_file = temp_dir().join("rust_llm_tui_edit_buffer.tmp");
    let editor_cmd_str = std::env::var("EDITOR").unwrap_or_else(|_| "emacsclient".to_string());
    let mut editor_os_cmd = OsCommand::new(editor_cmd_str);
    editor_os_cmd.arg(&temp_file);

    let mut line_editor = SuspendableReedline::create_with_history_and_hinter(
        "llm_tui_history.txt",
        Box::new(token_counting_hinter),
        app_arc.clone(),
    )
    .with_buffer_editor(editor_os_cmd, temp_file);

    enable_kitty_keyboard_for_meta_alt_modifiers();

    'main_loop: loop {
        // Check if there's a running API task and clean up if finished
        api_task_manager.check_and_cleanup_finished_task();

        if api_task_manager.is_task_running() {
            // Poll for Ctrl+C input while API task is running
            if let Some(abort_signal) = &api_task_manager.abort_signal {
                match poll_abort_signal(abort_signal) {
                    Ok(true) => {
                        // Ctrl+C detected, cancel the current task
                        api_task_manager.cancel_current_task();

                        // Clear AI thinking state
                        {
                            let mut app_locked = app_arc.lock().unwrap();
                            app_locked.ai_is_thinking = false;
                        }

                        if let Err(e) = print_rounded_message("Agent interrupted", ORANGE_COLOR) {
                            eprintln!("Error displaying agent interrupted message: {}", e);
                        }
                    }
                    Ok(false) => {
                        // No Ctrl+C detected, continue waiting
                    }
                    Err(e) => {
                        eprintln!("Error polling for abort signal: {}", e);
                    }
                }
            } else {
                // Fallback to sleep if no abort signal available
                tokio::time::sleep(Duration::from_millis(25)).await;
            }
            continue;
        }

        // The TokenCountingHinter will automatically update the buffer state on every character input
        let read_result = line_editor.read_line(&token_counting_prompt);

        match read_result {
            Ok(ReadResult::Success(buffer)) => {
                let trimmed_buffer = buffer.trim();
                if trimmed_buffer == "/exit" {
                    api_task_manager.cancel_current_task();
                    break 'main_loop;
                } else if trimmed_buffer == "/clear" {
                    println!("\nCleared all messages.\n");
                    let messages_to_reprint;
                    {
                        // Lock scope
                        let app_locked = app_arc.lock().unwrap();
                        messages_to_reprint = app_locked.messages.clone();
                    } // Lock released
                    for msg in messages_to_reprint {
                        display::print_formatted_message(&msg)?;
                    }
                    continue;
                } else if trimmed_buffer == "/help" {
                    print_help();
                    continue;
                } else if trimmed_buffer == "/session" {
                    match copy_to_clipboard(&*session_id.clone()) {
                        Ok(_) => {
                            let message = format!(
                                "Session ID copied to clipboard: {}",
                                session_id.clone().green()
                            );
                            if let Err(e) = display::print_command_output(&*message) {
                                eprintln!("Error displaying session message: {}", e);
                            }
                        }
                        Err(e) => {
                            eprintln!("Failed to copy session ID to clipboard: {}", e);
                        }
                    }
                    continue;
                } else if trimmed_buffer == "/last" {
                    let last_content = {
                        let app_locked = app_arc.lock().unwrap();
                        app_locked.get_last_ai_message_content()
                    };

                    match last_content {
                        Some(content) => match copy_to_clipboard(&content) {
                            Ok(_) => {
                                let message = "Last AI response copied to clipboard";
                                if let Err(e) = display::print_command_output(message) {
                                    eprintln!("Error displaying last message: {}", e);
                                }
                            }
                            Err(e) => {
                                eprintln!("Failed to copy last response to clipboard: {}", e);
                            }
                        },
                        None => {
                            let message = "No AI responses found";
                            if let Err(e) = display::print_command_output(message) {
                                eprintln!("Error displaying no response message: {}", e);
                            }
                        }
                    }
                    continue;
                } else if trimmed_buffer == "/all" {
                    let all_content = {
                        let app_locked = app_arc.lock().unwrap();
                        app_locked.get_all_messages_for_api()
                    };

                    match copy_to_clipboard(&all_content) {
                        Ok(_) => {
                            let message = "All conversation history copied to clipboard";
                            if let Err(e) = display::print_command_output(message) {
                                eprintln!("Error displaying all message: {}", e);
                            }
                        }
                        Err(e) => {
                            eprintln!("Failed to copy conversation history to clipboard: {}", e);
                        }
                    }
                    continue;
                } else if trimmed_buffer.starts_with("/model") {
                    let parts: Vec<&str> = trimmed_buffer.split_whitespace().collect();
                    if parts.len() == 1 {
                        // Show current model and available models
                        let (current_model, available_models) = {
                            let app_locked = app_arc.lock().unwrap();
                            (app_locked.current_model, app_locked.get_available_models())
                        };
                        let mut message = format!("Current model: {}\n\nAvailable models:", current_model.startup_banner_text());
                        for model in available_models {
                            message.push_str(&format!("\n• {} - {}", model.to_string_value(), model.startup_banner_text()));
                        }
                        message.push_str("\n\nUsage: /model <model-name>");
                        if let Err(e) = display::print_command_output(&message) {
                            eprintln!("Error displaying model info: {}", e);
                        }
                    } else if parts.len() == 2 {
                        // Change model
                        let model_name = parts[1];
                        if let Some(new_model) = cli::ModelType::from_str(model_name) {
                            let result = {
                                let mut app_locked = app_arc.lock().unwrap();
                                app_locked.change_model(new_model)
                            };
                            match result {
                                Ok(message) => {
                                    if let Err(e) = display::print_command_output(&message.green().to_string()) {
                                        eprintln!("Error displaying model change message: {}", e);
                                    }
                                }
                                Err(error) => {
                                    if let Err(e) = display::print_command_output(&error.red().to_string()) {
                                        eprintln!("Error displaying model change error: {}", e);
                                    }
                                }
                            }
                        } else {
                            let error_msg = format!("Unknown model: {}. Use /model to see available models.", model_name);
                            if let Err(e) = display::print_command_output(&error_msg.red().to_string()) {
                                eprintln!("Error displaying model error: {}", e);
                            }
                        }
                    } else {
                        let error_msg = "Usage: /model [model-name]";
                        if let Err(e) = display::print_command_output(&error_msg.red().to_string()) {
                            eprintln!("Error displaying model usage: {}", e);
                        }
                    }
                    continue;
                } else if trimmed_buffer.is_empty() {
                    continue;
                }

                // Create abort signal for this API call
                let abort_signal = create_abort_signal();

                // Set AI thinking state
                {
                    let mut app_locked = app_arc.lock().unwrap();
                    app_locked.ai_is_thinking = true;
                }

                // Spawn the API task
                let app_arc_clone = app_arc.clone();
                let http_client_clone = http_client.clone();
                let session_file_path_clone = session_file_path.clone();
                let cli_args_clone = cli_args.clone();
                let abort_signal_clone = abort_signal.clone();

                let task = tokio::spawn(async move {
                    let result = process_user_input(
                        buffer,
                        app_arc_clone.clone(),
                        &http_client_clone,
                        &session_file_path_clone,
                        &cli_args_clone,
                        &abort_signal_clone,
                        true, // Display user message for regular input
                    )
                    .await;

                    // Clear AI thinking state when done
                    {
                        let mut app_locked = app_arc_clone.lock().unwrap();
                        app_locked.ai_is_thinking = false;
                    }

                    if let Err(e) = result {
                        eprintln!("Error processing user input: {}", e);
                    }
                });

                api_task_manager.start_task(task, abort_signal);
            }
            Ok(ReadResult::ShouldQuit) => {
                api_task_manager.cancel_current_task();
                app_arc.lock().unwrap().should_quit = true;
            }
            Ok(ReadResult::DoNothing) => {}
            Ok(ReadResult::InterruptAI) => {
                // Handle AI interruption (same as ClearBuffer when AI is thinking)
                if api_task_manager.is_task_running() {
                    api_task_manager.cancel_current_task();

                    // Clear AI thinking state
                    {
                        let mut app_locked = app_arc.lock().unwrap();
                        app_locked.ai_is_thinking = false;
                    }

                    // Add a user cancellation message to the conversation
                    let cancellation_message = AppMessage {
                        sender: MessageSender::User,
                        parts: vec![MessageContent::Text("User aborted request".to_string())],
                    };

                    {
                        let mut app_locked = app_arc.lock().unwrap();
                        app_locked.messages.push(cancellation_message.clone());
                        app_locked
                            .add_user_message_to_llm_history("User aborted request".to_string());

                        // Save the session with the cancellation message
                        if let Err(e) = session_manager::save_session(
                            &session_file_path,
                            &app_locked.conversation_history_for_llm,
                        ) {
                            eprintln!("{}", format!("Error saving session: {}", e).red());
                        }
                    }

                    if let Err(e) = print_rounded_message("Agent interrupted", ORANGE_COLOR) {
                        eprintln!("Error displaying agent interrupted message: {}", e);
                    }

                    // Display the cancellation message
                    if let Err(e) = display::print_formatted_message(&cancellation_message) {
                        eprintln!("Error displaying cancellation message: {}", e);
                    }
                }
            }
            Err(err) => {
                eprintln!("SuspendableReedline error: {:?}. Exiting.", err);
                api_task_manager.cancel_current_task();
                app_arc.lock().unwrap().should_quit = true;
            }
        }

        if app_arc.lock().unwrap().should_quit {
            break 'main_loop;
        }
    }

    disable_kitty_keyboard_for_meta_alt_modifiers();
    Ok(())
}
