# top prio

show API errors, currently it always fails

fix broken tools after showing API errors

change execute tool to run shell command always in current repo dir

# Core

even with instantdummy, the initial startup is sluggish, as well as sending a message which is weird

if --restore is passed with empty string, use latest session based on the session directories last modified

# UI

make ai messages border a green color, use a new color in src/display/colors.rs
and also on first line display it like this
╭─ Response ──────────────────────────────────────────────────────────────────────────────╮

show menu completion when /

it has options to have a menu, how is it implemented, later for confirming if running a shell command is allowed:

the repl
temp) .rag                                                                                                591(0.06%)
⚙ Initializing RAG...
? Select embedding model:
> gemini:text-embedding-004 (max-tokens:2048;max-batch:100;price:0)
[↑↓ to move, enter to select, type to filter]

ensure that after a heading is only 1 newline
cargo run --example markdown_rendering
has 2 newlines in bottom, here:
│                                                        h6                                                        │
│                                                                                                                  │
│                                                                                                                  │
│ Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore    │

# Refactor

using find and wc, refactor all files above 300 lines

needs runtime model change capabilities, but only for ones with tool support
add gemini 2.0 flash provider (use https://ai.google.dev/gemini-api/docs/openai?hl=de)
add cohere provider

# switch to async-openai crate for more correct tiktoken usage for token counting

use ChatCompletionRequestMessage

---

add a test first that tools affect token count!

Counting max_tokens parameter for a chat completion request with async-openai
rewrite to use ChatCompletionRequestMessage as shown below

use tiktoken_rs::async_openai::get_chat_completion_max_tokens;
use async_openai::types::{ChatCompletionRequestMessage, Role};

let messages = vec![
ChatCompletionRequestMessage {
content: Some("You are a helpful assistant that only speaks French.".to_string()),
role: Role::System,
name: None,
function_call: None,
},
ChatCompletionRequestMessage {
content: Some("Hello, how are you?".to_string()),
role: Role::User,
name: None,
function_call: None,
},
ChatCompletionRequestMessage {
content: Some("Parlez-vous francais?".to_string()),
role: Role::System,
name: None,
function_call: None,
},
];
let max_tokens = get_chat_completion_max_tokens("o1-mini", &messages).unwrap();
println!("max_tokens: {}", max_tokens);