use rust_llm_tui::llm_client::tools::create_tools;

#[test]
fn test_tools_creation() {
    // Simple test to verify tools can be created and serialized
    let tools = create_tools();

    // Verify we have some tools
    assert!(!tools.is_empty(), "Should have at least one tool");

    // Verify tools can be serialized to JSON (this affects token count)
    let tools_json = serde_json::to_string(&tools).expect("Failed to serialize tools");
    assert!(!tools_json.is_empty(), "Tools JSON should not be empty");

    println!("Created {} tools", tools.len());
    println!("Tools JSON length: {} characters", tools_json.len());
}

#[test]
fn test_chat_message_structure() {
    // Test that we can work with our internal ChatMessage structure
    use rust_llm_tui::llm_client::ChatMessage;

    let message = ChatMessage {
        role: "user".to_string(),
        content: "Hello world".to_string(),
        tool_calls: None,
        tool_call_id: None,
    };

    assert_eq!(message.role, "user");
    assert_eq!(message.content, "Hello world");
    assert!(message.tool_calls.is_none());
    assert!(message.tool_call_id.is_none());
}
